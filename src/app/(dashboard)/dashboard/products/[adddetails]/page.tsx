"use client";
import DashLoading from "@/app/(dashboard)/loading";
import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { Anton, Manrope } from "next/font/google"
import Image from "next/image";
import { useRef, useState } from "react";
import { AiOutlineUnorderedList } from "react-icons/ai";
import { BiItalic } from "react-icons/bi";
import { ImBold } from "react-icons/im";
import { PiTextAUnderlineBold } from "react-icons/pi";
import { TbCapture } from "react-icons/tb";

type Params = {
    params: { adddetails: string }
}

type ProductDetails = {
    title?: string | null,
    items?: string | null,
    regular?: number| null,
    discount?:number| null,
    discrate?:number| null,
    brandtitle?:string| null,
    brandimg?:File | string | null,
    colorCode?: string | null,
    dressSize?: string | null,
}

type SponserData = {
    id: number,
    title: string | null,
    imgpath: string | null
}

type StoreImage = {
    imgFile1: File | null,
    imgFile2: File | null,
    imgFile3: File | null,
    imgFile4: File | null,
}

const manrope = Manrope({
    subsets:["latin"]
})

const anton = Anton({
    subsets: ["latin"],
    weight: "400"
})
export default function AddDetails({ params }: Params) {
    const [productDetails,setDetails] = useState<ProductDetails>();
    const [colorCode,setColorCode] = useState<string[]>([]);
    const [dressSize,setSize] = useState<string[]>([]);
    const [editorTxt,setEditorTxt] = useState<string>("");
    const editorRef = useRef<HTMLDivElement|null>(null);

    const [storeImage,setStore] = useState<StoreImage>({
        imgFile1:null,
        imgFile2:null,
        imgFile3:null,
        imgFile4:null
    });

    const {isLoading:sponserLoad,isError:sponserError,data:sponserData} = useQuery<SponserData[]>({
        queryKey:["sponserImg"],
        queryFn: async ()=>{
            const response = await axios("/api/sponserdataretrieve");
            const result = response.data.data;
            
            return result as SponserData[];
        }
    });

    const handlerInputChange=(event:React.ChangeEvent<HTMLInputElement>)=>{
        const {name,value,files} = event.target;
        const existed = files?.[0];

        if(name == "brandimg"){
            if(existed){
                setDetails({...productDetails,brandimg:existed});
            }else{
                setDetails({...productDetails,brand})
            }
            
        }else{
            setDetails({...productDetails,[name]:value});
        }
        
    }

    const handleImgInputChange=(event:React.ChangeEvent<HTMLInputElement>)=>{
        const {name,files} = event.target;
        const existed = files?.[0];

        setStore({...storeImage,[name]:existed});
    }

    const reuseableInput=(placeholder:string | null,value:string | number | null,name:string)=>{
        return <>
        <input type="text" value={value ?? ""} name={name} autoComplete="off" className={`${manrope.className} h-full w-full px-3 placeholder:capitalize placeholder:text-black/40 placeholder:font-bold text-black/80 font-medium focus:outline-black/10 bg-white rounded-lg shadow-xs shadow-black/10`} placeholder={`${placeholder}`}
        onChange={(event)=>{handlerInputChange(event)}}
        />
        </>
    }

    const getValue=(condition:string)=>{
        const copy = condition == "colorCode" ? colorCode : dressSize;
        const newValue = condition == "colorCode" ? productDetails?.colorCode : productDetails?.dressSize;
        const update = newValue ? [...(copy || []), newValue] : [...(copy || [])];

        if(condition == "colorCode"){
            setColorCode(update)
            setDetails({...productDetails,colorCode:""})
        }else{
            setSize(update);
            setDetails({...productDetails,dressSize:""})
        }
    }

    const textEditorAction=(action:string)=>{
        document.execCommand(action,false);
    }
    return (
        <>
        <section className="mt-5 px-5 pb-20">
            <div className="grid grid-cols-2 gap-x-5">
                <div>
                    <div className="flex flex-row gap-x-5">
                        {(Object.keys(storeImage) as (keyof typeof storeImage)[]).map((items,index)=>{
                            return <div className="h-[120px] w-full shadow-xs shadow-black/20 rounded-lg bg-white relative overflow-hidden" key={index}>
                            {
                                storeImage[`${items}`]?
                                <label htmlFor={`${items}`} className="absolute h-full w-full flex justify-center items-center">
                                <input type="file" name={`${items}`} id={`${items}`} accept="image/*" className="hidden"
                                onChange={(event)=>{handleImgInputChange(event)}}
                                />
                                <Image src={storeImage[`${items}`] instanceof File ? URL.createObjectURL(storeImage[`${items}`] as File): ""} alt="productImage" fill/>
                                <span className="text-4xl text-black/20">
                                    <TbCapture />
                                </span>
                            </label>:
                            <label htmlFor={`${items}`} className="absolute h-full w-full flex justify-center items-center">
                                <input type="file" name={`${items}`} id={`${items}`} accept="image/*" className="hidden"
                                onChange={(event)=>{handleImgInputChange(event)}}
                                />
                                
                                <span className="text-4xl text-black/20">
                                    <TbCapture />
                                </span>
                            </label>
                            }
                        </div>
                        })}
                    </div>

                    <div className="mt-10">
                        <h2 className={`${anton.className} uppercase text-4xl font-bold tracking-widest`} style={{WebkitTextStroke:'2px black',WebkitTextFillColor:"transparent"}}>
                            details
                        </h2>

                        <div className={`${manrope.className} mt-5 h-[250px] w-full p-2 resize-none focus:outline-black/20 text-black/80 text-base placeholder:font-medium placeholder:text-2xl placeholder:tracking-wider rounded-lg bg-white shadow-sm shadow-black/20 textEditor overflow-y-auto`} contentEditable suppressContentEditableWarning ref={editorRef}>
                            
                        </div>
                        <div>
                            <pre>
                                {editorTxt}
                            </pre>
                        </div>
                        <div className="flex flex-row gap-x-5 mt-5 items-center justify-end px-4">
                            <button className="p-[10px] bg-white rounded-xl transition-all duration-150 ease-linear hover:cursor-pointer shadow-[1px_1px_4px_black]/20 hover:shadow-[4px_4px_2px_black]/50" onClick={()=>{textEditorAction("bold")}}>
                                <ImBold />
                            </button>
                            <button className="p-[10px] bg-white rounded-xl transition-all duration-150 ease-linear hover:cursor-pointer shadow-[1px_1px_4px_black]/20 hover:shadow-[4px_4px_2px_black]/50"  onClick={()=>{textEditorAction("italic")}}>
                                <BiItalic />
                            </button>
                            <button className="p-[10px] bg-white rounded-xl transition-all duration-150 ease-linear hover:cursor-pointer shadow-[1px_1px_4px_black]/20 hover:shadow-[4px_4px_2px_black]/50"  onClick={()=>{textEditorAction("underline")}}>
                                <PiTextAUnderlineBold />
                            </button>
                            <button className="p-[10px] bg-white rounded-xl transition-all duration-150 ease-linear hover:cursor-pointer shadow-[1px_1px_4px_black]/20 hover:shadow-[4px_4px_2px_black]/50"  onClick={()=>{textEditorAction("insertUnorderedList")}}>
                                <AiOutlineUnorderedList />
                            </button>
                        </div>
                    </div>
                </div>

                <div>
                    <div className="flex flex-row gap-x-5">
                        <div className="h-10 w-full">
                            {reuseableInput("title",productDetails?.title ?? null,"title")}
                        </div>
                        <div className="h-10 w-full relative group">
                            {reuseableInput("items",productDetails?.items ?? null,"items")}

                            <div className="absolute w-full rounded-xl top-10 py-2 opacity-0 pointer-events-none transition-all duration-200 ease-linear group-hover:opacity-100 group-hover:pointer-events-auto">
                                <div className="py-5 px-2.5 bg-white shadow-sm shadow-black/20 rounded-xl">
                                    <span className={`${manrope.className} text-center text-black/20`}>
                                        No items found
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div>
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    Color
                                </h3>
                            </div>
                            <div className="h-10 w-full">
                            {reuseableInput("color code",productDetails?.colorCode ?? null,"colorCode")}
                            </div>

                            <div className="w-full flex flex-row justify-end">
                                <button className={`${manrope.className} bg-[#2ecc71] px-3 py-2 rounded-xl text-white font-bold shadow-[2px_2px_2px_#16a085] transition-all duration-200 ease-linear hover:cursor-pointer active:scale-95 hover:shadow-[1px_1px_1px_#16a085] hover:bg-[#27ae60]`} onClick={()=>{getValue("colorCode")}}>
                                    add
                                </button>
                            </div>
                        </div>

                        <div className="mt-[10px] w-full px-5 py-5 bg-white rounded-lg shadow-xs shadow-black/20 flex flex-row flex-wrap gap-x-5 gap-y-5">
                            {
                                colorCode.map((items,index)=>{
                                    return <div key={index} className="h-10 w-10 rounded-full" style={{backgroundColor:`${items}`}}>

                                    </div>
                                })
                            }
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div>
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    size
                                </h3>
                            </div>
                            <div className="h-10 w-full">
                            {reuseableInput("dress size",productDetails?.dressSize ?? null, "dressSize")}
                            </div>

                            <div className="w-full flex flex-row justify-end">
                                <button className={`${manrope.className} bg-[#2ecc71] px-3 py-2 rounded-xl text-white font-bold shadow-[2px_2px_2px_#16a085] transition-all duration-200 ease-linear hover:cursor-pointer active:scale-95 hover:shadow-[1px_1px_1px_#16a085] hover:bg-[#27ae60]`} onClick={()=>{getValue("size")}}>
                                    add
                                </button>
                            </div>
                        </div>

                        <div className="mt-[10px] w-full px-5 py-5 bg-white rounded-lg shadow-xs shadow-black/20 flex flex-row flex-wrap gap-x-5 gap-y-5">
                            {
                                dressSize.map((items,index)=>{
                                    return <div key={index} className="bg-[#bdc3c7]/80 rounded-full px-3 py-2 shadow-[3px_2px_2px_#dfe6e9]">
                                        <span className={`${manrope.className} text-sm font-bold text-[#222f3e] tracking-wide `}>
                                            {items}
                                        </span>
                                    </div>
                                })
                            }
                        </div>
                    </div>

                    <div className="flex flex-row gap-x-5 mt-5">
                        <div className="h-10 w-full">
                            {reuseableInput("regular price",productDetails?.regular ?? null,"regular")}
                        </div>

                        <div className="h-10 w-full">
                            {reuseableInput("discount price",productDetails?.discount ?? null,"discount")}
                        </div>

                        <div className="h-10 w-full">
                            {reuseableInput("discount rate",productDetails?.discrate ?? null,"discrate")}
                        </div>
                    </div>

                    <div className="mt-5">
                        <div className="flex flex-row gap-x-5 items-center">
                            <div className="w-full">
                                <h3 className={`${anton.className} text-2xl font-medium tracking-wider`}>
                                    Brand title
                                </h3>
                            </div>
                            <div className="h-10 w-full relative group">
                            {reuseableInput("brand title",productDetails?.brandtitle ?? null,"brandtitle")}

                            <div className="absolute w-full rounded-xl top-10 py-2 opacity-0 pointer-events-none transition-all duration-200 ease-linear group-hover:opacity-100 group-hover:pointer-events-auto z-20">
                                {
                                    sponserLoad?
                                    <div>
                                        <DashLoading/>
                                    </div>:
                                    sponserError?
                                    <div>
                                        <h4>something went wrong</h4>
                                    </div>:
                                    <div className="flex flex-col gap-y-2.5 bg-white shadow-sm shadow-black/20 rounded-xl py-5 px-5">
                                        {
                                            sponserData?.map((items,index)=>{
                                                return <span className={`${manrope.className} text-black/50 transition-all duration-150 ease-linear hover:bg-black/20 px-4 rounded-lg`} key={index} onClick={()=>{setDetails({...productDetails,brandtitle:items.title})}}>
                                                    {items.title}
                                                </span>
                                            }) ?? 
                                            <div className="py-5 px-2.5 bg-white shadow-sm shadow-black/20 rounded-xl">
                                                <span className={`${manrope.className} text-center text-black/20`}>
                                                    No items found
                                                </span>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                            </div>

                            <div className="w-[5%]">
                                
                            </div>
                        </div>

                        <div className="mt-[10px] w-full h-20 bg-white rounded-xl shadow-xs shadow-black/20 relative overflow-hidden group">
                            <label htmlFor="brandimg" className="h-full w-full absolute flex justify-center items-center">
                                <input type="file" name="brandimg" id="brandimg" accept="image/*" className="hidden" onChange={(event)=>{handlerInputChange(event)}}/>

                                {
                                    productDetails?.brandimg ? 
                                    <Image src={productDetails.brandimg instanceof File ? URL.createObjectURL(productDetails.brandimg): `${productDetails.brandimg}`} alt="brandimg" fill className=" h-[90%] object-contain"/>
                                    : null
                                }

                                <span className="text-4xl text-black/20 transition-all duration-150 ease-linear group-hover:text-black group-hover:scale-105">
                                    <TbCapture />
                                </span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div className="mt-10">
                <button className={`${anton.className} bg-[#3498db] px-4 py-2 text-white tracking-widest text-xl rounded-xl shadow-[4px_3px_2px_#2980b9] transition-all duration-75 ease-linear hover:shadow-[3px_3px_4px_#3498db] hover:bg-[#2980b9] hover:cursor-pointer`}>
                    add new product
                </button>
            </div>
        </section>
        </>
    )
}